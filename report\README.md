# Vue3管理系统

一个基于Vue3 + Element Plus + Vue Router的现代化管理系统模板。

## 技术栈

- **前端框架**: Vue 3.5.17
- **UI组件库**: Element Plus 2.6.1
- **路由管理**: Vue Router 4.3.0
- **网络请求**: Axios 1.10.0
- **构建工具**: Vite 7.0.4
- **样式预处理器**: SCSS 1.71.0
- **开发语言**: TypeScript/JavaScript

## 项目特性

- 🚀 **现代化架构**: 基于Vue3 Composition API
- 🎨 **美观界面**: 使用Element Plus组件库
- 📱 **响应式设计**: 支持多设备适配
- 🔐 **权限控制**: 路由守卫和登录验证
- 🌐 **网络封装**: 统一的axios请求封装
- 📦 **工程化配置**: 完善的开发和生产环境配置
- 🎨 **SCSS支持**: 强大的样式预处理器，支持变量、嵌套、混合器等特性

## 项目结构

```
src/
├── api/                    # API接口封装
│   ├── index.js           # API统一导出
│   ├── user.js            # 用户相关API
│   └── common.js          # 通用API
├── components/            # 公共组件
├── router/               # 路由配置
│   └── index.js
├── styles/               # 样式文件
│   ├── index.scss        # 主样式文件
│   ├── variables.scss    # 变量定义
│   ├── reset.scss        # CSS重置
│   ├── global.scss       # 全局样式
│   ├── utilities.scss    # 工具类
│   └── element-plus.scss # Element Plus样式覆盖
├── utils/                # 工具函数
│   └── request.js        # axios封装
├── views/                # 页面组件
│   ├── home/             # 首页
│   ├── login/            # 登录页
│   ├── dashboard/        # 仪表板
│   ├── user/             # 用户管理
│   └── error/            # 错误页面
├── assets/               # 静态资源
├── App.vue              # 根组件
└── main.js              # 入口文件
```

## 快速开始

### 安装依赖

```bash
npm install
# 或
pnpm install
```

### 启动开发服务器

```bash
npm run dev
# 或
pnpm dev
```

### 构建生产版本

```bash
npm run build
# 或
pnpm build
```

### 预览生产版本

```bash
npm run preview
# 或
pnpm preview
```

## 功能模块

### 1. 用户认证
- 登录/登出功能
- 路由权限控制
- Token管理

### 2. 首页
- 系统概览
- 快速操作入口
- 系统信息展示

### 3. 仪表板
- 数据统计卡片
- 图表展示区域
- 最近活动时间线

### 4. 用户管理
- 用户列表展示
- 新增/编辑/删除用户
- 搜索和筛选功能
- 分页显示

## API接口

项目已封装了完整的API请求模块：

### 用户相关API
- `userApi.login()` - 用户登录
- `userApi.register()` - 用户注册
- `userApi.getUserInfo()` - 获取用户信息
- `userApi.updateUserInfo()` - 更新用户信息
- `userApi.logout()` - 用户登出

### 通用API
- `commonApi.getSystemConfig()` - 获取系统配置
- `commonApi.uploadFile()` - 文件上传
- `commonApi.getDataList()` - 获取数据列表
- `commonApi.createData()` - 创建数据
- `commonApi.updateData()` - 更新数据
- `commonApi.deleteData()` - 删除数据

## 环境配置

项目支持多环境配置，可以通过环境变量文件进行配置：

```bash
# 开发环境
VITE_APP_TITLE=Vue3管理系统
VITE_API_BASE_URL=/api
VITE_APP_ENV=development
```

## 开发指南

### 添加新页面

1. 在 `src/views/` 目录下创建新的页面组件
2. 在 `src/router/index.js` 中添加路由配置
3. 根据需要添加API接口

### 添加新API

1. 在 `src/api/` 目录下创建新的API文件
2. 在 `src/api/index.js` 中导出新API
3. 在组件中使用API

### 自定义组件

1. 在 `src/components/` 目录下创建组件
2. 使用Element Plus组件库
3. 遵循Vue3 Composition API规范
4. 使用SCSS编写样式，导入变量文件

### 样式开发

1. 在 `src/styles/` 目录下管理样式文件
2. 使用SCSS变量和嵌套语法
3. 参考 `docs/scss-guide.md` 了解详细用法
4. 遵循BEM命名规范和模块化组织

## 部署说明

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
npm run build
npm run preview
```

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发团队。
