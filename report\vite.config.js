import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import collector from '../sdk/collector.mjs'

const env = loadEnv(null, process.cwd())
console.log(env)
const collInstance = await collector.init(
	env.VITE_username,
	env.VITE_gitServer,
	env.VITE_gitToken,
	env.VITE_station
)
console.log(collInstance)

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    open: true,
    cors: true,
    proxy: {
			'/api/': {
				// target: `https://${env.VITE_station}`,
				target: `https://127.0.0.1:3000`,
				changeOrigin: true,
				agent: collInstance.agent,
				headers: {
					'Authorization': `Bearer ${collInstance.token}`
				}
			}
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  }
})
