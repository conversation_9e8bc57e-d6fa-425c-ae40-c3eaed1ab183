<template>
  <div class="dashboard-container">
    <el-header class="header">
      <div class="header-content">
        <div class="logo">
          <h2>仪表板</h2>
        </div>
        <div class="user-info">
          <el-dropdown @command="handleCommand">
            <span class="el-dropdown-link">
              管理员 <el-icon><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                <el-dropdown-item command="settings">设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <el-main class="main-content">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-content">
              <div class="stats-icon" style="background-color: #409EFF;">
                <el-icon size="30" color="white"><user /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.userCount }}</div>
                <div class="stats-label">用户总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-content">
              <div class="stats-icon" style="background-color: #67C23A;">
                <el-icon size="30" color="white"><document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.orderCount }}</div>
                <div class="stats-label">订单总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-content">
              <div class="stats-icon" style="background-color: #E6A23C;">
                <el-icon size="30" color="white"><money /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">¥{{ stats.revenue }}</div>
                <div class="stats-label">总收入</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-content">
              <div class="stats-icon" style="background-color: #F56C6C;">
                <el-icon size="30" color="white"><view /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.visitCount }}</div>
                <div class="stats-label">访问量</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>用户增长趋势</span>
              </div>
            </template>
            <div class="chart-placeholder">
              <el-empty description="图表区域" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>收入统计</span>
              </div>
            </template>
            <div class="chart-placeholder">
              <el-empty description="图表区域" />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 最近活动 -->
      <el-card class="activity-card">
        <template #header>
          <div class="card-header">
            <span>最近活动</span>
          </div>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in recentActivities"
            :key="index"
            :timestamp="activity.time"
            :type="activity.type"
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </el-main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowDown,
  User,
  Document,
  Money,
  View
} from '@element-plus/icons-vue'

/**
 * 响应式数据
 */
const router = useRouter()

const stats = ref({
  userCount: 1234,
  orderCount: 5678,
  revenue: '123,456',
  visitCount: 9876
})

const recentActivities = ref([
  {
    content: '新用户注册',
    time: '2024-01-15 10:30:00',
    type: 'primary'
  },
  {
    content: '订单完成',
    time: '2024-01-15 09:15:00',
    type: 'success'
  },
  {
    content: '系统更新',
    time: '2024-01-15 08:00:00',
    type: 'warning'
  },
  {
    content: '数据备份',
    time: '2024-01-15 07:30:00',
    type: 'info'
  }
])

/**
 * 处理下拉菜单命令
 */
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'settings':
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      ElMessage.success('退出登录成功')
      localStorage.removeItem('token')
      router.push('/login')
      break
  }
}

/**
 * 组件挂载时获取数据
 */
onMounted(() => {
  // 这里可以调用API获取仪表板数据
  console.log('仪表板页面已加载')
})
</script>

<style scoped>
.dashboard-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #545c64;
  color: white;
  padding: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
}

.logo h2 {
  margin: 0;
  color: white;
}

.user-info {
  color: white;
}

.el-dropdown-link {
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.main-content {
  padding: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 300px;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 