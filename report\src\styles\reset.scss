/**
 * CSS重置样式
 */

// 导入变量
@import './variables.scss';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 14px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  font-size: $font-size-base;
  color: $text-regular;
  background-color: $background-color-base;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 链接样式
a {
  color: $primary-color;
  text-decoration: none;
  transition: color 0.3s ease;
  
  &:hover {
    color: lighten($primary-color, 10%);
  }
  
  &:active {
    color: darken($primary-color, 10%);
  }
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
  border-style: none;
}

// 表单元素
input,
button,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}

// 按钮重置
button {
  border: none;
  background: none;
  cursor: pointer;
  outline: none;
  
  &:disabled {
    cursor: not-allowed;
  }
}

// 列表重置
ul,
ol {
  list-style: none;
}

// 表格重置
table {
  border-collapse: collapse;
  border-spacing: 0;
}

// 清除浮动
.clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 文本选择样式
::selection {
  background-color: rgba($primary-color, 0.2);
  color: $text-primary;
}

::-moz-selection {
  background-color: rgba($primary-color, 0.2);
  color: $text-primary;
} 