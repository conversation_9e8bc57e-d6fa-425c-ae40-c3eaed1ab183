import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import router from './router'
import './styles/index.scss'
import App from './App.vue'

/**
 * 创建Vue应用实例
 */
const app = createApp(App)

/**
 * 注册Element Plus图标
 */
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

/**
 * 使用插件
 */
app.use(ElementPlus)
app.use(router)

/**
 * 挂载应用
 */
app.mount('#app')
