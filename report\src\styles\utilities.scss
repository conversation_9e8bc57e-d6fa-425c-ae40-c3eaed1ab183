/**
 * 工具类样式
 */

// 导入变量
@import './variables.scss';

// 显示/隐藏
.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

// Flex布局
.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.flex-1 {
  flex: 1 !important;
}

.flex-auto {
  flex: auto !important;
}

.flex-none {
  flex: none !important;
}

// 文本对齐
.text-left {
  text-align: left !important;
}

.text-center {
  text-align: center !important;
}

.text-right {
  text-align: right !important;
}

.text-justify {
  text-align: justify !important;
}

// 文本装饰
.text-decoration-none {
  text-decoration: none !important;
}

.text-decoration-underline {
  text-decoration: underline !important;
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

// 文本转换
.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

// 字体权重
.font-weight-light {
  font-weight: 300 !important;
}

.font-weight-normal {
  font-weight: 400 !important;
}

.font-weight-medium {
  font-weight: 500 !important;
}

.font-weight-bold {
  font-weight: 700 !important;
}

// 字体样式
.font-style-normal {
  font-style: normal !important;
}

.font-style-italic {
  font-style: italic !important;
}

// 行高
.line-height-1 {
  line-height: 1 !important;
}

.line-height-sm {
  line-height: 1.25 !important;
}

.line-height-base {
  line-height: 1.5 !important;
}

.line-height-lg {
  line-height: 2 !important;
}

// 边框
.border {
  border: 1px solid $border-color-base !important;
}

.border-0 {
  border: 0 !important;
}

.border-top {
  border-top: 1px solid $border-color-base !important;
}

.border-right {
  border-right: 1px solid $border-color-base !important;
}

.border-bottom {
  border-bottom: 1px solid $border-color-base !important;
}

.border-left {
  border-left: 1px solid $border-color-base !important;
}

// 边框圆角
.rounded {
  border-radius: $border-radius-base !important;
}

.rounded-sm {
  border-radius: $border-radius-small !important;
}

.rounded-lg {
  border-radius: $border-radius-large !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

// 阴影
.shadow {
  box-shadow: $box-shadow-base !important;
}

.shadow-sm {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.shadow-lg {
  box-shadow: $box-shadow-dark !important;
}

.shadow-none {
  box-shadow: none !important;
}

// 位置
.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: sticky !important;
}

// 溢出
.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-visible {
  overflow: visible !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

// 宽度
.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

// 高度
.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

// 光标
.cursor-pointer {
  cursor: pointer !important;
}

.cursor-default {
  cursor: default !important;
}

.cursor-not-allowed {
  cursor: not-allowed !important;
}

// 用户选择
.user-select-none {
  user-select: none !important;
}

.user-select-auto {
  user-select: auto !important;
}

.user-select-all {
  user-select: all !important;
}

// 垂直对齐
.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

// 空白处理
.white-space-normal {
  white-space: normal !important;
}

.white-space-nowrap {
  white-space: nowrap !important;
}

.white-space-pre {
  white-space: pre !important;
}

.white-space-pre-wrap {
  white-space: pre-wrap !important;
}

.white-space-pre-line {
  white-space: pre-line !important;
}

// 单词换行
.word-break-normal {
  word-break: normal !important;
}

.word-break-break-all {
  word-break: break-all !important;
}

.word-break-keep-all {
  word-break: keep-all !important;
}

.word-break-break-word {
  word-break: break-word !important;
} 