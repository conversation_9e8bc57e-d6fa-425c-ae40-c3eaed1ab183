import request from '@/utils/request'

/**
 * 用户相关API接口
 */
export const userApi = {
  /**
   * 用户登录
   * @param {Object} data - 登录参数
   * @param {string} data.username - 用户名
   * @param {string} data.password - 密码
   * @returns {Promise} 登录结果
   */
  login(data) {
    return request({
      url: '/user/login',
      method: 'post',
      data
    })
  },

  /**
   * 用户注册
   * @param {Object} data - 注册参数
   * @param {string} data.username - 用户名
   * @param {string} data.password - 密码
   * @param {string} data.email - 邮箱
   * @returns {Promise} 注册结果
   */
  register(data) {
    return request({
      url: '/user/register',
      method: 'post',
      data
    })
  },

  /**
   * 获取用户信息
   * @returns {Promise} 用户信息
   */
  getUserInfo() {
    return request({
      url: '/user/info',
      method: 'get'
    })
  },

  /**
   * 更新用户信息
   * @param {Object} data - 用户信息
   * @returns {Promise} 更新结果
   */
  updateUserInfo(data) {
    return request({
      url: '/user/update',
      method: 'put',
      data
    })
  },

  /**
   * 用户登出
   * @returns {Promise} 登出结果
   */
  logout() {
    return request({
      url: '/user/logout',
      method: 'post'
    })
  },
    /**
   * 获取用户活动
   * @returns {Promise} 用户活动数据
   */
    getUserActivity() {
      return request({
        url: '/user-activity',
        method: 'get'
      })
    }
} 