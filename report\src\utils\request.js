import axios from 'axios'
import { ElMessage } from 'element-plus'

/**
 * 创建axios实例
 */
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api', // 从环境变量获取基础URL
  timeout: 15000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
})

/**
 * 请求拦截器
 */
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    
    // 可以在这里添加loading状态
    console.log('发送请求:', config.url)
    
    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

/**
 * 响应拦截器
 * 处理统一的响应格式:
 * {
 *   success: boolean,
 *   count?: number,
 *   data: any,
 *   pagination?: {
 *     page: number,
 *     pageSize: number,
 *     totalPages: number,
 *     total: number
 *   },
 *   sort?: {
 *     field: string,
 *     order: string
 *   },
 *   message?: string
 * }
 */
service.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    const { data, status } = response
    
    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据
    if (status === 200) {
      // 根据新的响应结构进行处理
      if (data.success === true) {
        // 请求成功，返回完整的响应数据
        return data
      } else {
        // 业务错误处理
        const errorMessage = data.message || '请求失败'
        ElMessage.error(errorMessage)
        return Promise.reject(new Error(errorMessage))
      }
    }
    
    return data
  },
  error => {
    // 对响应错误做点什么
    console.error('响应错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          // 可以在这里处理登录过期逻辑
          localStorage.removeItem('token')
          // router.push('/login')
          break
        case 403:
          ElMessage.error('拒绝访问')
          break
        case 404:
          ElMessage.error('请求错误，未找到该资源')
          break
        case 500:
          ElMessage.error('服务器端出错')
          break
        default:
          ElMessage.error(data?.message || `连接错误${status}`)
      }
    } else if (error.request) {
      ElMessage.error('网络错误，请检查您的网络连接')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

export default service 