import request from '@/utils/request'

/**
 * 通用API接口
 */
export const commonApi = {
  /**
   * 获取系统配置
   * @returns {Promise} 系统配置信息
   */
  getSystemConfig() {
    return request({
      url: '/system/config',
      method: 'get'
    })
  },

  /**
   * 上传文件
   * @param {FormData} data - 文件数据
   * @returns {Promise} 上传结果
   */
  uploadFile(data) {
    return request({
      url: '/upload/file',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 获取数据列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string} params.keyword - 搜索关键词
   * @returns {Promise} 数据列表
   */
  getDataList(params) {
    return request({
      url: '/data/list',
      method: 'get',
      params
    })
  },

  /**
   * 获取数据详情
   * @param {string|number} id - 数据ID
   * @returns {Promise} 数据详情
   */
  getDataDetail(id) {
    return request({
      url: `/data/detail/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建数据
   * @param {Object} data - 数据对象
   * @returns {Promise} 创建结果
   */
  createData(data) {
    return request({
      url: '/data/create',
      method: 'post',
      data
    })
  },

  /**
   * 更新数据
   * @param {string|number} id - 数据ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 更新结果
   */
  updateData(id, data) {
    return request({
      url: `/data/update/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除数据
   * @param {string|number} id - 数据ID
   * @returns {Promise} 删除结果
   */
  deleteData(id) {
    return request({
      url: `/data/delete/${id}`,
      method: 'delete'
    })
  }
} 