<template>
  <div class="home-container">
    <!-- 头部导航 -->
    <el-header class="header">
      <div class="header-content">
        <div class="logo">
          <h2>SmartAgi AI分析系统</h2>
        </div>
        <div class="nav-menu">
          <el-menu
            mode="horizontal"
            :default-active="activeIndex"
            @select="handleSelect"
            background-color="#545c64"
            text-color="#fff"
            active-text-color="#ffd04b"
          >
            <el-menu-item index="home">首页</el-menu-item>
            <el-menu-item index="dashboard">仪表板</el-menu-item>
            <el-menu-item index="user">用户管理</el-menu-item>
          </el-menu>
        </div>
        <div class="user-info">
          <el-dropdown @command="handleCommand">
            <span class="el-dropdown-link">
              管理员 <el-icon><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                <el-dropdown-item command="settings">设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <!-- 主要内容区域 -->
    <el-main class="main-content">
      <!-- 用户活动展示区域 -->
      <el-card class="activity-card">
        <template #header>
          <div class="card-header">
            <span>用户活动数据</span>
            <el-button size="small" @click="fetchUserActivity" :loading="loadingActivity">
              刷新数据
            </el-button>
          </div>
        </template>
        
        <!-- 加载状态 -->
        <el-skeleton v-if="loadingActivity" rows="4" animated />
        
        <!-- 空状态 -->
        <el-empty v-else-if="!userActivity.length" description="暂无用户活动数据" />
        
        <!-- 用户活动列表 -->
        <div v-else>
          <el-alert
            title="接口联调成功"
            type="success"
            :closable="false"
            style="margin-bottom: 16px;"
          >
            <template #default>
              <p>成功调用 <code>/api/user-activity</code> 接口</p>
              <p>返回数据条数：{{ userActivity.length }}</p>
            </template>
          </el-alert>
          
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in userActivity"
              :key="index"
              :timestamp="item.time || item.timestamp || new Date().toLocaleString()"
              :type="item.type || 'primary'"
            >
              <h4>{{ item.title || item.content || `活动 ${index + 1}` }}</h4>
              <p>{{ item.description || item.content || JSON.stringify(item) }}</p>
            </el-timeline-item>
          </el-timeline>
        </div>
        
        <!-- 原始响应数据展示 -->
        <el-divider content-position="left">原始响应数据</el-divider>
        <el-input
          v-model="rawResponse"
          type="textarea"
          :rows="8"
          placeholder="接口响应的原始数据将在这里显示..."
          readonly
        />
      </el-card>
    </el-main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowDown,
  Monitor,
  Setting,
  DataAnalysis,
  DataBoard,
  User,
  QuestionFilled
} from '@element-plus/icons-vue'
import { userApi } from '@/api/user'

/**
 * 响应式数据
 */
const activeIndex = ref('home')
const router = useRouter()

// 用户活动相关数据
const userActivity = ref([])
const loadingActivity = ref(false)
const rawResponse = ref('')

/**
 * 处理菜单选择
 */
const handleSelect = (key) => {
  switch (key) {
    case 'home':
      router.push('/')
      break
    case 'dashboard':
      router.push('/dashboard')
      break
    case 'user':
      router.push('/user')
      break
  }
}

/**
 * 处理下拉菜单命令
 */
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'settings':
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      ElMessage.success('退出登录成功')
      localStorage.removeItem('token')
      router.push('/login')
      break
  }
}

/**
 * 跳转到仪表板
 */
const goToDashboard = () => {
  router.push('/dashboard')
}

/**
 * 跳转到用户管理
 */
const goToUser = () => {
  router.push('/user')
}

/**
 * 显示设置
 */
const showSettings = () => {
  ElMessage.info('系统设置功能开发中...')
}

/**
 * 显示帮助
 */
const showHelp = () => {
  ElMessage.info('帮助文档功能开发中...')
}

/**
 * 获取用户活动数据
 */
const fetchUserActivity = async () => {
  loadingActivity.value = true
  rawResponse.value = ''
  
  try {
    ElMessage.info('正在调用 /api/user-activity 接口...')
    const response = await userApi.getUserActivity()
    
    // 保存原始响应数据
    rawResponse.value = JSON.stringify(response, null, 2)
    
    // 处理响应数据
    if (response && response.data) {
      userActivity.value = Array.isArray(response.data) ? response.data : [response.data]
    } else if (Array.isArray(response)) {
      userActivity.value = response
    } else {
      userActivity.value = [response]
    }
    
    ElMessage.success(`接口调用成功！获取到 ${userActivity.value.length} 条数据`)
  } catch (error) {
    console.error('获取用户活动失败:', error)
    rawResponse.value = JSON.stringify(error.response || error, null, 2)
    ElMessage.error('获取用户活动失败: ' + (error.message || '未知错误'))
  } finally {
    loadingActivity.value = false
  }
}

/**
 * 组件挂载时自动调用接口
 */
onMounted(() => {
  fetchUserActivity()
})
</script>

<style lang="scss" scoped>
// 导入变量
@import '@/styles/variables.scss';

.home-container {
  min-height: 100vh;
  background-color: $background-color-base;
}

.header {
  background-color: #545c64;
  color: white;
  padding: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 $spacing-lg;
}

.logo {
  h2 {
    margin: 0;
    color: white;
  }
}

.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
}

.user-info {
  color: white;
}

.el-dropdown-link {
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.main-content {
  padding: $spacing-lg;
}

.welcome-card {
  margin-bottom: $spacing-lg;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-content {
  padding: $spacing-lg 0;
}

.feature-card {
  text-align: center;
  padding: $spacing-lg;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  
  .feature-icon {
    margin-bottom: $spacing-md;
  }
  
  h3 {
    margin: $spacing-sm 0;
    color: $text-primary;
  }
  
  p {
    color: $text-regular;
    line-height: 1.6;
  }
}

.quick-actions {
  margin-bottom: $spacing-lg;
  
  .el-button {
    width: 100%;
    height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
}

.system-info {
  margin-bottom: $spacing-lg;
}

.activity-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  h4 {
    margin: 0 0 8px 0;
    color: $text-primary;
  }
  
  p {
    margin: 0;
    color: $text-regular;
    line-height: 1.5;
  }
  
  code {
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
  }
}
</style> 