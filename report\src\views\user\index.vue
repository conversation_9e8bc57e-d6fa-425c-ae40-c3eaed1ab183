<template>
  <div class="user-container">
    <el-header class="header">
      <div class="header-content">
        <div class="logo">
          <h2>用户管理</h2>
        </div>
        <div class="user-info">
          <el-dropdown @command="handleCommand">
            <span class="el-dropdown-link">
              管理员 <el-icon><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                <el-dropdown-item command="settings">设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <el-main class="main-content">
      <!-- 搜索和操作区域 -->
      <el-card class="search-card">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入用户名或邮箱"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.status" placeholder="用户状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="正常" value="active" />
              <el-option label="禁用" value="inactive" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><search /></el-icon>
              搜索
            </el-button>
          </el-col>
          <el-col :span="4">
            <el-button type="success" @click="handleAdd">
              <el-icon><plus /></el-icon>
              新增用户
            </el-button>
          </el-col>
        </el-row>
      </el-card>

      <!-- 用户列表 -->
      <el-card class="table-card">
        <el-table
          :data="userList"
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="username" label="用户名" width="120" />
          <el-table-column prop="email" label="邮箱" width="200" />
          <el-table-column prop="nickname" label="昵称" width="120" />
          <el-table-column prop="role" label="角色" width="100">
            <template #default="{ row }">
              <el-tag :type="row.role === 'admin' ? 'danger' : 'primary'">
                {{ row.role === 'admin' ? '管理员' : '普通用户' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'info'">
                {{ row.status === 'active' ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="handleEdit(row)">编辑</el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>

      <!-- 用户表单对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="500px"
        @close="handleDialogClose"
      >
        <el-form
          ref="userFormRef"
          :model="userForm"
          :rules="userRules"
          label-width="80px"
        >
          <el-form-item label="用户名" prop="username">
            <el-input v-model="userForm.username" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="userForm.email" placeholder="请输入邮箱" />
          </el-form-item>
          <el-form-item label="昵称" prop="nickname">
            <el-input v-model="userForm.nickname" placeholder="请输入昵称" />
          </el-form-item>
          <el-form-item label="角色" prop="role">
            <el-select v-model="userForm.role" placeholder="请选择角色">
              <el-option label="普通用户" value="user" />
              <el-option label="管理员" value="admin" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="userForm.status">
              <el-radio label="active">正常</el-radio>
              <el-radio label="inactive">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="!userForm.id" label="密码" prop="password">
            <el-input
              v-model="userForm.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>
    </el-main>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowDown,
  Search,
  Plus
} from '@element-plus/icons-vue'

/**
 * 响应式数据
 */
const router = useRouter()
const loading = ref(false)
const dialogVisible = ref(false)
const submitLoading = ref(false)
const userFormRef = ref()

const searchForm = reactive({
  keyword: '',
  status: ''
})

const userForm = reactive({
  id: '',
  username: '',
  email: '',
  nickname: '',
  role: 'user',
  status: 'active',
  password: ''
})

const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

const userList = ref([
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    nickname: '管理员',
    role: 'admin',
    status: 'active',
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    username: 'user1',
    email: '<EMAIL>',
    nickname: '用户1',
    role: 'user',
    status: 'active',
    createTime: '2024-01-02 11:00:00'
  },
  {
    id: 3,
    username: 'user2',
    email: '<EMAIL>',
    nickname: '用户2',
    role: 'user',
    status: 'inactive',
    createTime: '2024-01-03 12:00:00'
  }
])

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 3
})

/**
 * 计算属性
 */
const dialogTitle = computed(() => {
  return userForm.id ? '编辑用户' : '新增用户'
})

/**
 * 处理下拉菜单命令
 */
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'settings':
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      ElMessage.success('退出登录成功')
      localStorage.removeItem('token')
      router.push('/login')
      break
  }
}

/**
 * 搜索用户
 */
const handleSearch = () => {
  loading.value = true
  // 模拟搜索
  setTimeout(() => {
    loading.value = false
    ElMessage.success('搜索完成')
  }, 1000)
}

/**
 * 新增用户
 */
const handleAdd = () => {
  resetUserForm()
  dialogVisible.value = true
}

/**
 * 编辑用户
 */
const handleEdit = (row) => {
  Object.assign(userForm, row)
  userForm.password = '' // 编辑时不显示密码
  dialogVisible.value = true
}

/**
 * 删除用户
 */
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除用户 "${row.username}" 吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 模拟删除
    const index = userList.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      userList.value.splice(index, 1)
      pagination.total--
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 取消删除
  })
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!userFormRef.value) return
  
  try {
    await userFormRef.value.validate()
    submitLoading.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (userForm.id) {
      // 编辑
      const index = userList.value.findIndex(item => item.id === userForm.id)
      if (index > -1) {
        Object.assign(userList.value[index], userForm)
      }
      ElMessage.success('编辑成功')
    } else {
      // 新增
      const newUser = {
        ...userForm,
        id: Date.now(),
        createTime: new Date().toLocaleString()
      }
      userList.value.push(newUser)
      pagination.total++
      ElMessage.success('新增成功')
    }
    
    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitLoading.value = false
  }
}

/**
 * 重置用户表单
 */
const resetUserForm = () => {
  Object.assign(userForm, {
    id: '',
    username: '',
    email: '',
    nickname: '',
    role: 'user',
    status: 'active',
    password: ''
  })
}

/**
 * 对话框关闭处理
 */
const handleDialogClose = () => {
  resetUserForm()
  if (userFormRef.value) {
    userFormRef.value.resetFields()
  }
}

/**
 * 分页大小改变
 */
const handleSizeChange = (size) => {
  pagination.pageSize = size
  // 重新加载数据
}

/**
 * 当前页改变
 */
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  // 重新加载数据
}

/**
 * 组件挂载时获取数据
 */
onMounted(() => {
  // 这里可以调用API获取用户列表
  console.log('用户管理页面已加载')
})
</script>

<style lang="scss" scoped>
// 导入变量
@import '@/styles/variables.scss';

.user-container {
  min-height: 100vh;
  background-color: $background-color-base;
}

.header {
  background-color: #545c64;
  color: white;
  padding: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 $spacing-lg;
}

.logo {
  h2 {
    margin: 0;
    color: white;
  }
}

.user-info {
  color: white;
}

.el-dropdown-link {
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.main-content {
  padding: $spacing-lg;
}

.search-card {
  margin-bottom: $spacing-lg;
}

.table-card {
  margin-bottom: $spacing-lg;
}

.pagination-wrapper {
  margin-top: $spacing-lg;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 