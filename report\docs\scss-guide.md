# SCSS 使用指南

本项目已配置SCSS预处理器，提供更强大的CSS编写能力。

## 项目结构

```
src/styles/
├── index.scss          # 主样式文件，导入所有模块
├── variables.scss      # 变量定义
├── reset.scss         # CSS重置样式
├── global.scss        # 全局样式
├── utilities.scss     # 工具类
└── element-plus.scss  # Element Plus样式覆盖
```

## 变量使用

### 颜色变量
```scss
// 主色调
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 文字颜色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #C0C4CC;
```

### 间距变量
```scss
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;
```

### 字体变量
```scss
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 13px;
$font-size-extra-small: 12px;
```

## 在组件中使用SCSS

### 1. 导入变量
```vue
<style lang="scss" scoped>
@import '@/styles/variables.scss';

.my-component {
  color: $text-primary;
  padding: $spacing-lg;
  font-size: $font-size-base;
}
</style>
```

### 2. 嵌套语法
```scss
.card {
  background: white;
  border-radius: $border-radius-base;
  
  &__header {
    padding: $spacing-md;
    border-bottom: 1px solid $border-color-light;
    
    h3 {
      margin: 0;
      color: $text-primary;
    }
  }
  
  &__body {
    padding: $spacing-lg;
  }
  
  &:hover {
    box-shadow: $box-shadow-light;
  }
}
```

### 3. 混合器(Mixins)
```scss
// 定义混合器
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin responsive($breakpoint) {
  @if $breakpoint == mobile {
    @media (max-width: $breakpoint-sm) { @content; }
  } @else if $breakpoint == tablet {
    @media (max-width: $breakpoint-md) { @content; }
  } @else if $breakpoint == desktop {
    @media (max-width: $breakpoint-lg) { @content; }
  }
}

// 使用混合器
.container {
  @include flex-center;
  
  @include responsive(mobile) {
    flex-direction: column;
  }
}
```

### 4. 函数
```scss
// 颜色函数
.button {
  background-color: $primary-color;
  
  &:hover {
    background-color: lighten($primary-color, 10%);
  }
  
  &:active {
    background-color: darken($primary-color, 10%);
  }
}

// 数学运算
.sidebar {
  width: calc(100% - #{$spacing-lg * 2});
  margin: $spacing-md;
}
```

## 工具类

项目提供了丰富的工具类，可以直接在模板中使用：

### 间距类
```html
<div class="mt-lg mb-md pl-sm pr-lg">
  <!-- 上边距24px，下边距16px，左内边距8px，右内边距24px -->
</div>
```

### 布局类
```html
<div class="d-flex justify-content-center align-items-center">
  <!-- flex布局，水平垂直居中 -->
</div>
```

### 文本类
```html
<div class="text-center font-weight-bold text-primary">
  <!-- 文本居中，粗体，主色调 -->
</div>
```

## 响应式设计

### 断点变量
```scss
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1920px;
```

### 响应式工具类
```html
<div class="hidden-xs">在小屏幕上隐藏</div>
<div class="hidden-sm">在中等屏幕上隐藏</div>
<div class="hidden-md">在大屏幕上隐藏</div>
```

### 自定义响应式样式
```scss
.responsive-component {
  width: 100%;
  
  @media (max-width: $breakpoint-sm) {
    width: 50%;
  }
  
  @media (max-width: $breakpoint-md) {
    width: 75%;
  }
}
```

## Element Plus 样式覆盖

项目提供了Element Plus组件的样式覆盖，可以通过CSS变量自定义主题：

```scss
:root {
  --el-color-primary: #{$primary-color};
  --el-color-success: #{$success-color};
  --el-color-warning: #{$warning-color};
  --el-color-danger: #{$danger-color};
  --el-color-info: #{$info-color};
}
```

## 最佳实践

### 1. 使用变量
```scss
// 好的做法
.button {
  background-color: $primary-color;
  padding: $spacing-md;
}

// 避免硬编码
.button {
  background-color: #409EFF;
  padding: 16px;
}
```

### 2. 嵌套不要过深
```scss
// 好的做法
.card {
  &__header {
    &__title {
      font-size: $font-size-large;
    }
  }
}

// 避免过深嵌套
.card {
  &__header {
    &__title {
      &__text {
        &__link {
          color: $primary-color;
        }
      }
    }
  }
}
```

### 3. 使用BEM命名规范
```scss
.user-card {
  &__header {
    padding: $spacing-md;
  }
  
  &__body {
    padding: $spacing-lg;
  }
  
  &--highlighted {
    border: 2px solid $primary-color;
  }
}
```

### 4. 模块化组织
```scss
// 将相关样式组织在一起
.form {
  &__group {
    margin-bottom: $spacing-md;
  }
  
  &__label {
    color: $text-primary;
    font-weight: $font-weight-primary;
  }
  
  &__input {
    border-radius: $border-radius-base;
    
    &:focus {
      border-color: $primary-color;
    }
  }
  
  &__error {
    color: $danger-color;
    font-size: $font-size-small;
  }
}
```

## 编译和构建

Vite会自动处理SCSS文件的编译，无需额外配置。在开发模式下，SCSS文件会被实时编译，在生产构建时会被优化和压缩。

## 注意事项

1. 确保在组件中使用 `lang="scss"` 属性
2. 使用 `@import` 导入变量文件
3. 避免在SCSS中使用 `!important`，优先使用更具体的选择器
4. 保持样式的一致性和可维护性
5. 合理使用嵌套，避免过深的层级结构 