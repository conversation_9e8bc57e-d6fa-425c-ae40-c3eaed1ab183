/**
 * 全局样式
 */

// 导入变量
@import './variables.scss';

// 应用根元素
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: $background-color-light;
  border-radius: $border-radius-small;
}

::-webkit-scrollbar-thumb {
  background: $border-color-base;
  border-radius: $border-radius-small;
  
  &:hover {
    background: $text-secondary;
  }
}

// 页面容器
.page-container {
  min-height: 100vh;
  background-color: $background-color-base;
}

// 内容区域
.content-wrapper {
  padding: $spacing-lg;
}

// 卡片样式
.card-wrapper {
  background: white;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-light;
  padding: $spacing-lg;
  margin-bottom: $spacing-lg;
}

// 标题样式
.page-title {
  font-size: $font-size-extra-large;
  font-weight: $font-weight-primary;
  color: $text-primary;
  margin-bottom: $spacing-lg;
  
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 20px;
    background: $primary-color;
    margin-right: $spacing-sm;
    vertical-align: middle;
  }
}

.section-title {
  font-size: $font-size-large;
  font-weight: $font-weight-primary;
  color: $text-primary;
  margin-bottom: $spacing-md;
}

// 文本样式
.text-primary {
  color: $text-primary;
}

.text-regular {
  color: $text-regular;
}

.text-secondary {
  color: $text-secondary;
}

.text-placeholder {
  color: $text-placeholder;
}

// 间距工具类
.mt-xs { margin-top: $spacing-xs; }
.mt-sm { margin-top: $spacing-sm; }
.mt-md { margin-top: $spacing-md; }
.mt-lg { margin-top: $spacing-lg; }
.mt-xl { margin-top: $spacing-xl; }

.mb-xs { margin-bottom: $spacing-xs; }
.mb-sm { margin-bottom: $spacing-sm; }
.mb-md { margin-bottom: $spacing-md; }
.mb-lg { margin-bottom: $spacing-lg; }
.mb-xl { margin-bottom: $spacing-xl; }

.ml-xs { margin-left: $spacing-xs; }
.ml-sm { margin-left: $spacing-sm; }
.ml-md { margin-left: $spacing-md; }
.ml-lg { margin-left: $spacing-lg; }
.ml-xl { margin-left: $spacing-xl; }

.mr-xs { margin-right: $spacing-xs; }
.mr-sm { margin-right: $spacing-sm; }
.mr-md { margin-right: $spacing-md; }
.mr-lg { margin-right: $spacing-lg; }
.mr-xl { margin-right: $spacing-xl; }

.pt-xs { padding-top: $spacing-xs; }
.pt-sm { padding-top: $spacing-sm; }
.pt-md { padding-top: $spacing-md; }
.pt-lg { padding-top: $spacing-lg; }
.pt-xl { padding-top: $spacing-xl; }

.pb-xs { padding-bottom: $spacing-xs; }
.pb-sm { padding-bottom: $spacing-sm; }
.pb-md { padding-bottom: $spacing-md; }
.pb-lg { padding-bottom: $spacing-lg; }
.pb-xl { padding-bottom: $spacing-xl; }

.pl-xs { padding-left: $spacing-xs; }
.pl-sm { padding-left: $spacing-sm; }
.pl-md { padding-left: $spacing-md; }
.pl-lg { padding-left: $spacing-lg; }
.pl-xl { padding-left: $spacing-xl; }

.pr-xs { padding-right: $spacing-xs; }
.pr-sm { padding-right: $spacing-sm; }
.pr-md { padding-right: $spacing-md; }
.pr-lg { padding-right: $spacing-lg; }
.pr-xl { padding-right: $spacing-xl; }

// 响应式工具类
@media (max-width: $breakpoint-sm) {
  .hidden-xs {
    display: none !important;
  }
}

@media (max-width: $breakpoint-md) {
  .hidden-sm {
    display: none !important;
  }
}

@media (max-width: $breakpoint-lg) {
  .hidden-md {
    display: none !important;
  }
}

@media (max-width: $breakpoint-xl) {
  .hidden-lg {
    display: none !important;
  }
}

// 动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
} 