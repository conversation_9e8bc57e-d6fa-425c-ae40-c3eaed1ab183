/**
 * Element Plus 样式覆盖
 */

// 导入变量
@import './variables.scss';

// 覆盖Element Plus的CSS变量
:root {
  // 主色调
  --el-color-primary: #{$primary-color};
  --el-color-primary-light-3: #{lighten($primary-color, 30%)};
  --el-color-primary-light-5: #{lighten($primary-color, 50%)};
  --el-color-primary-light-7: #{lighten($primary-color, 70%)};
  --el-color-primary-light-8: #{lighten($primary-color, 80%)};
  --el-color-primary-light-9: #{lighten($primary-color, 90%)};
  --el-color-primary-dark-2: #{darken($primary-color, 20%)};

  // 成功色
  --el-color-success: #{$success-color};
  --el-color-success-light-3: #{lighten($success-color, 30%)};
  --el-color-success-light-5: #{lighten($success-color, 50%)};
  --el-color-success-light-7: #{lighten($success-color, 70%)};
  --el-color-success-light-8: #{lighten($success-color, 80%)};
  --el-color-success-light-9: #{lighten($success-color, 90%)};
  --el-color-success-dark-2: #{darken($success-color, 20%)};

  // 警告色
  --el-color-warning: #{$warning-color};
  --el-color-warning-light-3: #{lighten($warning-color, 30%)};
  --el-color-warning-light-5: #{lighten($warning-color, 50%)};
  --el-color-warning-light-7: #{lighten($warning-color, 70%)};
  --el-color-warning-light-8: #{lighten($warning-color, 80%)};
  --el-color-warning-light-9: #{lighten($warning-color, 90%)};
  --el-color-warning-dark-2: #{darken($warning-color, 20%)};

  // 危险色
  --el-color-danger: #{$danger-color};
  --el-color-danger-light-3: #{lighten($danger-color, 30%)};
  --el-color-danger-light-5: #{lighten($danger-color, 50%)};
  --el-color-danger-light-7: #{lighten($danger-color, 70%)};
  --el-color-danger-light-8: #{lighten($danger-color, 80%)};
  --el-color-danger-light-9: #{lighten($danger-color, 90%)};
  --el-color-danger-dark-2: #{darken($danger-color, 20%)};

  // 信息色
  --el-color-info: #{$info-color};
  --el-color-info-light-3: #{lighten($info-color, 30%)};
  --el-color-info-light-5: #{lighten($info-color, 50%)};
  --el-color-info-light-7: #{lighten($info-color, 70%)};
  --el-color-info-light-8: #{lighten($info-color, 80%)};
  --el-color-info-light-9: #{lighten($info-color, 90%)};
  --el-color-info-dark-2: #{darken($info-color, 20%)};

  // 文字颜色
  --el-text-color-primary: #{$text-primary};
  --el-text-color-regular: #{$text-regular};
  --el-text-color-secondary: #{$text-secondary};
  --el-text-color-placeholder: #{$text-placeholder};
  --el-text-color-disabled: #{$text-placeholder};

  // 边框颜色
  --el-border-color: #{$border-color-base};
  --el-border-color-light: #{$border-color-light};
  --el-border-color-lighter: #{$border-color-lighter};
  --el-border-color-extra-light: #{$border-color-extra-light};

  // 背景颜色
  --el-bg-color: #ffffff;
  --el-bg-color-page: #{$background-color-base};
  --el-bg-color-overlay: #ffffff;

  // 字体大小
  --el-font-size-extra-large: #{$font-size-extra-large};
  --el-font-size-large: #{$font-size-large};
  --el-font-size-medium: #{$font-size-medium};
  --el-font-size-base: #{$font-size-base};
  --el-font-size-small: #{$font-size-small};
  --el-font-size-extra-small: #{$font-size-extra-small};

  // 边框圆角
  --el-border-radius-base: #{$border-radius-base};
  --el-border-radius-small: #{$border-radius-small};
  --el-border-radius-large: #{$border-radius-large};
  --el-border-radius-round: #{$border-radius-round};

  // 阴影
  --el-box-shadow-base: #{$box-shadow-base};
  --el-box-shadow-light: #{$box-shadow-light};
  --el-box-shadow-dark: #{$box-shadow-dark};

  // 间距
  --el-spacing-xs: #{$spacing-xs};
  --el-spacing-sm: #{$spacing-sm};
  --el-spacing-md: #{$spacing-md};
  --el-spacing-lg: #{$spacing-lg};
  --el-spacing-xl: #{$spacing-xl};
}

// 自定义Element Plus组件样式
.el-header {
  --el-header-height: #{$header-height};
}

.el-main {
  --el-main-padding: #{$spacing-lg};
}

// 按钮样式优化
.el-button {
  &--primary {
    &:hover {
      background-color: lighten($primary-color, 10%);
      border-color: lighten($primary-color, 10%);
    }
    
    &:active {
      background-color: darken($primary-color, 10%);
      border-color: darken($primary-color, 10%);
    }
  }
}

// 卡片样式优化
.el-card {
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-light;
  border: 1px solid $border-color-light;
  
  &__header {
    padding: $spacing-md $spacing-lg;
    border-bottom: 1px solid $border-color-light;
    background-color: $background-color-light;
  }
  
  &__body {
    padding: $spacing-lg;
  }
}

// 表格样式优化
.el-table {
  border-radius: $border-radius-base;
  overflow: hidden;
  
  th {
    background-color: $background-color-light;
    color: $text-primary;
    font-weight: $font-weight-primary;
  }
  
  td {
    color: $text-regular;
  }
}

// 表单样式优化
.el-form {
  &-item {
    margin-bottom: $spacing-md;
    
    &__label {
      color: $text-primary;
      font-weight: $font-weight-primary;
    }
    
    &__content {
      color: $text-regular;
    }
  }
}

// 输入框样式优化
.el-input {
  &__inner {
    border-radius: $border-radius-base;
    
    &:focus {
      border-color: $primary-color;
    }
  }
}

// 选择器样式优化
.el-select {
  .el-input__inner {
    border-radius: $border-radius-base;
  }
}

// 分页样式优化
.el-pagination {
  text-align: center;
  margin-top: $spacing-lg;
  
  .el-pager li {
    border-radius: $border-radius-base;
    
    &.active {
      background-color: $primary-color;
      color: white;
    }
  }
}

// 消息提示样式优化
.el-message {
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-light;
}

// 对话框样式优化
.el-dialog {
  border-radius: $border-radius-large;
  overflow: hidden;
  
  &__header {
    background-color: $background-color-light;
    border-bottom: 1px solid $border-color-light;
  }
  
  &__body {
    padding: $spacing-lg;
  }
  
  &__footer {
    border-top: 1px solid $border-color-light;
    padding: $spacing-md $spacing-lg;
  }
}

// 菜单样式优化
.el-menu {
  border-right: none;
  
  &-item {
    &:hover {
      background-color: lighten($primary-color, 90%);
    }
    
    &.is-active {
      background-color: $primary-color;
      color: white;
    }
  }
}

// 下拉菜单样式优化
.el-dropdown-menu {
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-light;
  border: 1px solid $border-color-light;
}

// 标签样式优化
.el-tag {
  border-radius: $border-radius-base;
  font-weight: $font-weight-secondary;
}

// 时间线样式优化
.el-timeline {
  &-item {
    &__node {
      border-radius: 50%;
    }
    
    &__content {
      color: $text-regular;
    }
    
    &__timestamp {
      color: $text-secondary;
    }
  }
}

// 描述列表样式优化
.el-descriptions {
  &__header {
    margin-bottom: $spacing-md;
  }
  
  &__body {
    .el-descriptions__table {
      border-radius: $border-radius-base;
      overflow: hidden;
    }
  }
} 